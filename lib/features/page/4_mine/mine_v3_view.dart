import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/invite_code_entity.dart';
import 'package:wd/core/models/entities/user_vip_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/features/page/4_mine/video_coupon/video_coupon_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/balance/balance_card.dart';
import 'package:wd/shared/widgets/card/metric_card.dart';
import 'package:wd/shared/widgets/common_bottom_sheet.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/gradient_border.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../main/screens/main_screen_cubit.dart';
import 'mine_v2_cubit.dart';
import 'mine_v2_state.dart';

/// 我的
class MineV3Page extends BasePage {
  const MineV3Page({super.key});

  @override
  BasePageState<BasePage> getState() => _MineV2PageState();
}

class _MineV2PageState extends BasePageState<MineV3Page> {
  // final double _marginTop = 10.gw;
  bool hasUpdate = false;
  int _selectedLocaleIndex = 0;

  @override
  void initState() {
    super.initState();
    _updatePageTitle();
    SystemUtil.appUpdate().then((value) => setState(() => hasUpdate = value.$1));
  }

  void _updatePageTitle() => pageTitle = 'account'.tr();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updatePageTitle();
  }

  @override
  Widget left() {
    return InkWell(
      onTap: () {
        AuthUtil.checkIfLogin(() {
          sl<NavigatorService>().push(AppRouter.userAccountSecurity);
        });
      },
      child: Image.asset(
        "assets/images/toolBar/icon_toolBar_setting.png",
        width: 36.gw,
        height: 36.gw,
      ),
    );
  }

  @override
  Widget right() => InkWell(
        onTap: () => SystemUtil.contactService(),
        child: Image.asset(
          Assets.iconSupport,
          width: 36.gw,
          height: 36.gw,
        ),
      );

  /// 邀请码模块
  Widget _buildInviteCodeRow() {
    return BlocSelector<UserCubit, UserState, InviteCodeEntity>(
        selector: (state) => state.inviteInfo ?? InviteCodeEntity(),
        builder: (context, inviteInfo) {
          if (StringUtil.isEmpty(inviteInfo.inviteCode)) {
            return const SizedBox.shrink();
          }
          return InkWell(
            onTap: () {
              Clipboard.setData(ClipboardData(text: inviteInfo.inviteCode));
              GSEasyLoading.showToast('复制成功');
            },
            child: Row(
              children: [
                AutoSizeText(
                  inviteInfo.inviteCode,
                  style: context.textTheme.primary.fs16,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: Image.asset(
                    Assets.iconCopy_v2,
                    width: 24.gw,
                    height: 24.gw,
                  ),
                ),
              ],
            ),
          );
        });
  }

  Widget _buildFunctionButton(FunctionItem item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: item.onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(item.imageAssets, width: 48.gw, height: 48.gw),
          SizedBox(height: 8.gw),
          Text(
            item.text,
            style: TextStyle(fontSize: 13.fs, color: const Color(0xff50576D)),
          ),
        ],
      ),
    );
  }

  Widget _buildOtherFunctionListWidget() {
    final dataList = [
      FunctionItem(
        text: "statement".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_statement.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.userStatement)),
      ),
      FunctionItem(
        text: "orders".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_order.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.order)),
      ),
      FunctionItem(
        text: "my_notifications".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_message.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.notifications)),
      ),
      FunctionItem(
        text: "vip_center".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_vip.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.vipCenter)),
      ),
      FunctionItem(
        text: "my_wallet".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_wallet.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.myWallet)),
      ),
      FunctionItem(
        text: "security".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_security.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.userAccountSecurity)),
      ),
      FunctionItem(
        text: "agent_recruitment".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_agent.png",
        onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.agentRecruitment)),
      ),
      if (GlobalConfig.needShowVideoPage())
        FunctionItem(
          text: "video_coupon".tr(),
          imageAssets: "assets/images/mine/v3/icon_mine_video_vip.png",
          onPressed: () => _handleAuthAction(() => sl<NavigatorService>().push(AppRouter.videoCoupon)),
        ),
      FunctionItem(
        text: "download_app".tr(),
        imageAssets: "assets/images/mine/v3/icon_mine_download.png",
        onPressed: () => SystemUtil.goToAppDownload(),
      ),
    ];
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.gw),
        color: context.theme.cardColor,
      ),
      child: ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return _buildOtherListCell(dataList[index]);
          },
          separatorBuilder: (context, index) => Container(
                height: 0.5,
                color: context.theme.dividerColor,
              ),
          itemCount: dataList.length),
    );
  }

  _buildOtherListCell(FunctionItem item) {
    return InkWell(
      onTap: item.onPressed,
      child: SizedBox(
        height: 48.gw,
        child: Row(
          children: [
            Image.asset(item.imageAssets, width: 28.gw, height: 28.gw),
            SizedBox(width: 12.gw),
            AneText(
              item.text,
              style: context.textTheme.title.w500,
            ),
            const Spacer(),
            if (item.child != null) ...[
              item.child!,
              if (item.showNextIcon) ...[
                SizedBox(
                  width: 16.gw,
                )
              ],
            ],
            if (item.showNextIcon)
              SvgPicture.asset(
                "assets/images/common/icon_next.svg",
                width: 24.gw,
                height: 24.gw,
                colorFilter: ColorFilter.mode(context.colorTheme.textHighlight, BlendMode.srcIn),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return BlocSelector<UserCubit, UserState, bool>(
        selector: (state) => state.isLogin,
        builder: (context, isLogin) => isLogin
            ? CommonButton(
                title: "logout".tr(),
                height: 42.gw,
                onPressed: () {
                  CommonBottomSheet.show(
                    context: context,
                    backgroundColor: context.theme.cardColor,
                    header: BottomSheetHeader(
                      title: 'logout_tips'.tr(),
                      titleStyle: context.textTheme.title.fs20.w500,
                      showCloseButton: false,
                    ),
                    children: [
                      CommonButton(
                        title: 'logout'.tr(),
                        style: CommonButtonStyle.quaternary,
                        onPressed: () {
                          Navigator.pop(context);
                          context.read<MineV2Cubit>().logout();
                        },
                      ),
                      SizedBox(height: 8.gw),
                      CommonButton(
                        title: 'cancel'.tr(),
                        style: CommonButtonStyle.tertiary,
                        backgroundColor: context.colorTheme.highlightForeground,
                        borderColor: context.colorTheme.highlightForeground,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                    buttons: [],
                  );
                },
              )
            : const SizedBox.shrink());
  }

  Widget _buildVersionSection() {
    return Padding(
      padding: const EdgeInsets.only(top: 20.0),
      child: Text(
        'v${SystemUtil.version}_${SystemUtil.buildNumber}',
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  void _handleAuthAction(VoidCallback action) {
    AuthUtil.checkIfLogin(action);
  }

  /// *********************************************************************************
  /// 会员名称
  Widget _buildMemberWidget(MineV2State state) {
    return InkWell(
      onTap: () => sl<NavigatorService>().push(
        sl<UserCubit>().state.isLogin ? AppRouter.userProfile : AppRouter.login,
      ),
      child: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/images/mine/bg_user_profile.png"), fit: BoxFit.fill),
        ),
        padding: EdgeInsets.all(16.gw),
        child: BlocSelector<UserCubit, UserState, ({String avatarUrl, String userName, bool isLogin})>(
          selector: (state) => (
            avatarUrl: state.userInfo?.faceId.toString() ?? '',
            userName: state.userInfo?.nickName ?? '',
            isLogin: state.isLogin,
          ),
          builder: (context, userState) {
            final isLogin = userState.isLogin;
            final userName = isLogin ? userState.userName : "sign_in_tips".tr();
            return Row(
              children: [
                AuthUtil.getAvatarWidget(context, avatarStr: userState.avatarUrl, size: Size(56.gw, 60.gw)),
                SizedBox(width: 16.gw),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AneText(userName, style: context.textTheme.primary),
                        if (sl<UserCubit>().state.isLogin) ...[
                          const SizedBox(width: 5),
                          Icon(Icons.arrow_forward_ios_sharp, color: context.colorTheme.textTitle, size: 8.gw),
                        ],
                      ],
                    ),
                    if (sl<UserCubit>().state.isLogin) ...[
                      SizedBox(height: 5.gw),
                      _buildInviteCodeRow(),
                    ] else ...[
                      SizedBox(height: 5.gw),
                      CommonButton(
                        title: "${'login'.tr()}/${'sign_up'.tr()}",
                        style: CommonButtonStyle.quaternary,
                        width: 120.gw,
                        height: 33.gw,
                        radius: 17.gw,
                        fontSize: 14.gw,
                        onPressed: () {
                          AuthUtil.checkIfLogin(() {});
                        },
                      ),
                    ]
                  ],
                ),
                const Spacer(),
                _buildVipSection(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildVipSection() {
    return BlocSelector<UserCubit, UserState, ({bool isLogin, UserVipEntity? vipInfo})>(
      selector: (state) => (isLogin: state.isLogin, vipInfo: state.vipInfo),
      builder: (context, userState) {
        return GestureDetector(
          onTap: () => sl<NavigatorService>().push(AppRouter.vipCenter),
          child: Container(
            width: 58.gw,
            height: 58.gw,
            decoration: const BoxDecoration(
              image: DecorationImage(image: AssetImage("assets/images/mine/bg_mine_vip_border.png"), fit: BoxFit.fill),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 5.gw),
                AneText("VIP${userState.vipInfo?.vipLevel ?? 0}", style: context.textTheme.primary.fs11.w700),
                SizedBox(height: 3.gw),
                Container(
                  width: 39.gw,
                  height: 14.gw,
                  decoration: BoxDecoration(
                    color: context.colorTheme.textPrimary.withOpacity(0.09),
                    borderRadius: BorderRadius.circular(2.3.gw),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AneText("Get", style: context.textTheme.primary.fs8),
                      SizedBox(width: 4.6.gw),
                      Icon(Icons.arrow_forward_ios_sharp, color: context.colorTheme.textPrimary, size: 5.gw),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  /// *********************************************************************************
  /// 钱包+观影天数
  Widget _buildWalletNVideoVipDaysWidget() {
    return ConstrainedBox(
      constraints: BoxConstraints.tightFor(height: 65.gw),
      child: Row(
        children: [
          /// 我的钱包
          Expanded(
              child: BalanceCard(
            title: 'balance'.tr(),
            onClickRefresh: () async {
              await GameUtil.transferOutAllPlatform();
              sl<UserCubit>().fetchUserVip();
              return;
            },
          )),
          SizedBox(width: 2.gw),

          /// 剩余观影天数
          Expanded(
              child: BlocSelector<UserCubit, UserState, int>(
            selector: (state) => state.videoVipInfo?.days ?? 0,
            builder: (context, days) {
              return MetricCard(
                'viewing_days_left'.tr(),
                iconPath: "assets/images/mine/video_coupon/btn_mine_video_left.png",
                value: "$days",
                onTap: () {
                  AuthUtil.checkIfLogin(() => sl<NavigatorService>().push(AppRouter.videoCoupon));
                },
              );
            },
          )),
        ],
      ),
    );
  }

  /// *********************************************************************************
  /// 充值&提现 按钮
  Widget _buildRechargeSection() {
    return SizedBox(
      height: 41.gw,
      child: Row(
        children: [
          Expanded(
              child: CommonButton(
            title: 'recharge'.tr(),
            style: CommonButtonStyle.primary,
            height: 41.gw,
            onPressed: () {
              AuthUtil.checkIfLogin(() {
                sl<NavigatorService>().push(AppRouter.recharge);
              });
            },
          )),
          SizedBox(width: 16.gw),
          Expanded(
              child: CommonButton(
            title: 'withdraw'.tr(),
            style: CommonButtonStyle.secondary,
            height: 41.gw,
            onPressed: () {
              AuthUtil.checkIfLogin(() {
                sl<NavigatorService>().push(AppRouter.withdraw);
              });
            },
          ))
        ],
      ),
    );
  }

  /// *********************************************************************************
  /// 聊天banner
  Widget _buildChatBanner() {
    return InkWell(
      onTap: () => AuthUtil.checkIfLogin(() {
        sl<MainScreenCubit>().goToChatPage();
      }),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        height: 72.gw,
        decoration: const BoxDecoration(
          image: DecorationImage(image: AssetImage("assets/images/mine/bg_mine_chat.png"), fit: BoxFit.fill),
        ),
        child: Row(
          children: [
            Image.asset("assets/images/mine/icon_mine_chat_group.png", width: 40.gw, height: 40.gw),
            SizedBox(width: 12.gw),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                AneText(
                  'official_chatroom'.tr(),
                  style: context.textTheme.primary.fs16,
                ),
                SizedBox(height: 2.gw),
                AneText(
                  'join_community'.tr(),
                  style: context.textTheme.title,
                ),
              ],
            ),
            const Spacer(),
            CommonButton(
              title: 'tap_to_enter'.tr(),
              width: 105.gw,
              height: 33.gw,
            )
          ],
        ),
      ),
    );
  }

  /// *********************************************************************************
  /// 版本 language
  Widget _buildVersionNLanguageSection() {
    final dataList = [
      FunctionItem(
          text: "language".tr(),
          imageAssets: "assets/images/mine/v3/icon_mine_language.png",
          onPressed: () {
            CommonBottomSheet.show(
              context: context,
              maxHeight: 431.gw,
              backgroundColor: context.theme.cardColor,
              showGradientOverlay: true,
              gradientOverlayHeight: 100.0,
              isScrollControlled: false,
              contentPadding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 16.gw),
              header: BottomSheetHeader(
                title: 'language'.tr(),
                showCloseButton: false,
              ),
              buttons: [
                BottomSheetButton(
                  title: 'cancel'.tr(),
                  style: CommonButtonStyle.secondary,
                  backgroundColor: context.colorTheme.foregroundColor,
                  borderColor: context.colorTheme.borderA,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
                BottomSheetButton(
                  title: 'ok'.tr(),
                  style: CommonButtonStyle.primary,
                  backgroundColor: context.colorTheme.btnBgPrimary,
                  textColor: context.colorTheme.btnTitlePrimary,
                  borderColor: context.colorTheme.btnBorderPrimary,
                  onPressed: () {
                    // Apply the selected locale
                    final selectedLocale = kLocaleList[_selectedLocaleIndex];
                    final newLocale = selectedLocale.toLocale();

                    // Update LocaleUtil to ensure content-language header is updated
                    if (newLocale != LocaleUtil().appLocale) {
                      LocaleUtil().appLocale = newLocale;
                    }

                    Navigator.pop(context);
                  },
                ),
              ],
              children: [
                _buildLocaleWheelPciker(),
              ],
            );
          },
          child: Container(
            height: 29.gw,
            decoration: BoxDecoration(
              color: context.colorTheme.foregroundColor,
              borderRadius: BorderRadius.circular(6.gw),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10.gw),
            alignment: Alignment.center,
            child: AneText(
              _getCurrentLanguageName(),
              style: context.textTheme.title,
            ),
          )),
      FunctionItem(
          text: "current_version".tr(),
          imageAssets: "assets/images/mine/v3/icon_mine_version.png",
          onPressed: () {},
          showNextIcon: false,
          child: Container(
            height: 29.gw,
            decoration: BoxDecoration(
              color: context.colorTheme.foregroundColor,
              borderRadius: BorderRadius.circular(6.gw),
            ),
            padding: EdgeInsets.symmetric(horizontal: 10.gw),
            child: Row(
              children: [
                /// FIXME 这里需要根据是否有更新来显示
                if (1 == 1) ...[
                  Container(
                    width: 8.gw,
                    height: 8.gw,
                    decoration: BoxDecoration(
                      color: context.theme.primaryColor,
                      borderRadius: BorderRadius.circular(4.gw),
                    ),
                  ),
                  SizedBox(width: 4.gw),
                ],
                AneText(
                  'V${SystemUtil.version}+${SystemUtil.buildNumber}',
                  style: context.textTheme.title,
                ),
              ],
            ),
          )),
    ];
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.gw),
        color: context.theme.cardColor,
      ),
      child: ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return _buildOtherListCell(dataList[index]);
          },
          separatorBuilder: (context, index) => Container(
                height: 0.5,
                color: context.theme.dividerColor,
              ),
          itemCount: dataList.length),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
        listenWhen: (previous, current) => current.isLogin != previous.isLogin,
        listener: (context, userState) {
          if (!userState.isLogin) {
            sl<VideoCouponCubit>().resetState();
          }
        },
        child: BlocBuilder<MineV2Cubit, MineV2State>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.fromLTRB(20.gw, 27.gw, 20.gw, 0),
              child: Column(
                children: [
                  _buildMemberWidget(state),
                  SizedBox(height: 16.gw),
                  _buildWalletNVideoVipDaysWidget(),
                  SizedBox(height: 16.gw),
                  _buildRechargeSection(),
                  SizedBox(height: 16.gw),
                  _buildChatBanner(),
                  SizedBox(height: 16.gw),
                  _buildOtherFunctionListWidget(),
                  SizedBox(height: 16.gw),
                  _buildVersionNLanguageSection(),
                  SizedBox(height: 16.gw),
                  _buildLogoutButton(),
                  SizedBox(height: 16.gw),
                ],
              ),
            );
          },
        ));
  }

  String _getCurrentLanguageName() {
    final currentLocale = context.locale;
    const locales = kLocaleList;

    // Find current locale index
    int currentIndex = locales.indexWhere((locale) =>
        locale.languageCode == currentLocale.languageCode && locale.countryCode == currentLocale.countryCode);

    if (currentIndex == -1) currentIndex = 0;

    return locales[currentIndex].name;
  }

  Widget _buildLocaleWheelPciker() {
    final currentLocale = context.locale;
    const locales = kLocaleList;

    // Find current locale index
    int currentIndex = locales.indexWhere((locale) =>
        locale.languageCode == currentLocale.languageCode && locale.countryCode == currentLocale.countryCode);

    if (currentIndex == -1) currentIndex = 0;
    _selectedLocaleIndex = currentIndex;

    return StatefulBuilder(
      builder: (context, setState) {
        int selectedIndex = currentIndex;
        return SizedBox(
          height: 200.gw,
          child: CupertinoPicker(
            scrollController: FixedExtentScrollController(initialItem: selectedIndex),
            itemExtent: 45.gw,
            backgroundColor: Colors.transparent,
            selectionOverlay: Container(
              decoration: BoxDecoration(
                border: GradientBoxBorder(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.12),
                      Colors.white.withOpacity(0.0),
                    ],
                  ),
                  width: 1.gw,
                ),
                gradient: LinearGradient(
                  colors: [
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.15),
                    context.colorTheme.foregroundColor.withOpacity(0.0),
                  ],
                ),
              ),
            ),
            onSelectedItemChanged: (int index) {
              setState(() {
                selectedIndex = index;
                _selectedLocaleIndex = index;
              });
            },
            children: List.generate(locales.length, (index) {
              final locale = locales[index];
              final isSelected = _selectedLocaleIndex == index;

              return Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      locale.flagIcon,
                      width: 24.gw,
                      height: 24.gw,
                    ),
                    SizedBox(width: 8.gw),
                    AneText(
                      locale.name,
                      style: context.textTheme.regular.fs16.w500.copyWith(
                        color: isSelected
                            ? context.colorTheme.btnBgPrimary
                            : context.colorTheme.textPrimary.withOpacity(0.6),
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
        );
      },
    );
  }
}

class FunctionItem {
  final String text;
  final String imageAssets;
  final Widget? child;
  final VoidCallback onPressed;
  final bool showNextIcon;
  final bool showBadge;

  FunctionItem({
    required this.text,
    required this.imageAssets,
    required this.onPressed,
    this.child,
    this.showBadge = false,
    this.showNextIcon = true,
  });
}
