import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/base_stateful_page.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_bet_entity.dart';
import '../../../../../core/models/entities/commission_recharge_entity.dart';
import '../../../../../shared/widgets/common_tabbar.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../../shared/widgets/common_card.dart';
import 'commission_plan_cubit.dart';

class CommissionPlanView extends BasePage {
  const CommissionPlanView({super.key});

  @override
  BasePageState<BasePage> getState() => _CommissionPlanViewState();
}

class _CommissionPlanViewState extends BasePageState with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late VoidCallback _tabListener;

  @override
  void initState() {
    super.initState();
    pageTitle = 'commission_plan'.tr();
    _tabController = TabController(length: TeamType.values.length, vsync: this);
    _tabListener = () {
      setState(() {});
    };
    _tabController.addListener(_tabListener);
    context.read<CommissionPlanCubit>().fetchPromotionBetConfig();
    context.read<CommissionPlanCubit>().fetchCommissionPlanRecharge();
  }

  @override
  Widget right() {
    return _buildCommissionTypeDropdown(context, TeamType.values[_tabController.index]);
  }

  @override
  void dispose() {
    _tabController.removeListener(_tabListener);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: const [BetCommission(), RechargeCommission()],
    );
  }

  Widget _buildCommissionTypeDropdown(BuildContext context, TeamType selectedType) {
    return PopupMenuButton<TeamType>(
      onSelected: (TeamType type) {
        _tabController.animateTo(type.index);
      },
      offset: const Offset(0, 35),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
      ),
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<TeamType>(
          value: TeamType.bet,
          child: Row(
            children: [
              Text(
                'betting_commission'.tr(),
                style: selectedType == TeamType.bet ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
        PopupMenuItem<TeamType>(
          value: TeamType.recharge,
          child: Row(
            children: [
              Text(
                'recharge_commission'.tr(),
                style:
                    selectedType == TeamType.recharge ? context.textTheme.primary.w500 : context.textTheme.title.w500,
              ),
            ],
          ),
        ),
      ],
      child: Container(
        height: 32.gw,
        padding: EdgeInsets.symmetric(horizontal: 11.gw,),
        decoration: BoxDecoration(
          color: context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(10.gw),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getCommissionTypeLabel(selectedType),
              style: context.textTheme.primary.fs13.w500,
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.arrow_drop_down,
              size: 20.gw,
              color: context.colorTheme.textTitle,
            ),
          ],
        ),
      ),
    );
  }

  String _getCommissionTypeLabel(TeamType type) {
    switch (type) {
      case TeamType.bet:
        return 'betting_commission'.tr();
      case TeamType.recharge:
        return 'recharge_commission'.tr();
    }
  }
}

class BetCommission extends StatelessWidget {
  const BetCommission({super.key});

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          if (state.commissionBetNetState == NetState.loadingState) return const SizedBox.shrink();

          final tabItems =
              state.commissionBetEntity?.list?.map((item) => CommonTabBarItem(title: item.type ?? '')).toList() ?? [];

          final selectedIndex = state.commissionBetEntity?.list
                  ?.indexWhere((item) => item.type == state.selectedCommissionBetList?.type) ??
              0;

          return Column(
            children: [
              if (tabItems.isNotEmpty)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gw),
                  child: CommonTabBar(
                    tabItems,
                    style: CommonTabBarStyle.secondary,
                    currentIndex: selectedIndex >= 0 ? selectedIndex : 0,
                    onTap: (index) {
                      if (index < (state.commissionBetEntity?.list?.length ?? 0)) {
                        context
                            .read<CommissionPlanCubit>()
                            .filterCommissionBetList(state.commissionBetEntity?.list?[index].type ?? '');
                      }
                    },
                  ),
                ),
              SizedBox(height: 8.gw),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.gw),
                  child: AnimationLimiter(
                    child: ListView.builder(
                      itemCount: state.selectedCommissionBetList?.thirdList?.length ?? 0,
                      itemBuilder: (BuildContext context, int index) {
                        return AnimationConfiguration.staggeredList(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          child: ScaleAnimation(
                            scale: 0.5,
                            child: FadeInAnimation(
                              child: Padding(
                                padding: EdgeInsets.symmetric(vertical: 4.gw),
                                child: _buildTableSection(
                                    state.selectedCommissionBetList?.thirdList?[index] ?? CommissionBetListThirdList()),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTableSection(CommissionBetListThirdList item) {
    return Builder(
      builder: (context) {
        return CommonCard(
          radius: 16.gw,
          padding: 12.gw,
          color: context.theme.cardColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    height: 15.gw,
                    width: 5.gw,
                    decoration: BoxDecoration(
                      color: context.theme.primaryColor,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(12.gw),
                        bottomRight: Radius.circular(12.gw),
                      ),
                    ),
                  ),
                  SizedBox(width: 6.gw),
                  Text(
                    item.platformName ?? '',
                    style: context.textTheme.primary.fs16.w500.copyWith(
                      color: context.theme.primaryColor,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.gw),
              _buildSubColumnTable(item, context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSubColumnTable(CommissionBetListThirdList item, BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gw),
        border: Border.all(color: const Color(0xFF161616), width: 1),
      ),
      child: Column(
        children: [
          _buildSubColumnHeader(context),
          ..._buildSubColumnRows(item),
        ],
      ),
    );
  }

  Widget _buildSubColumnHeader(BuildContext context) {
    return SizedBox(
      height: 50.gw,
      child: Row(
        children: [
          // Team Level column
          Container(
            width: 94.gw,
            decoration: BoxDecoration(
              color: context.theme.primaryColor,
              borderRadius: const BorderRadius.only(topLeft: Radius.circular(8)),
              border: const Border(right: BorderSide(color: Color(0xFF161616), width: 1)),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      'team_level'.tr(),
                      style: TextStyle(
                        color: const Color(0xFF101010),
                        fontSize: 14.fs,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Sub-columns for each level
          Expanded(
            child: Column(
              children: [
                // Main level headers
                SizedBox(
                  height: 25.gw,
                  child: Row(
                    children: [
                      _buildMainSubHeader('first_level_subordinate'.tr(), isFirst: true),
                      _buildMainSubHeader('second_level_subordinate'.tr()),
                      _buildMainSubHeader('third_level_subordinate'.tr(), isLast: true),
                    ],
                  ),
                ),
                // Sub-headers (Rate/Max)
                SizedBox(
                  height: 25.gw,
                  child: Row(
                    children: [
                      // First level sub-headers
                      _buildSubHeader('rate'.tr()),
                      _buildSubHeader('max'.tr()),
                      // Second level sub-headers
                      _buildSubHeader('rate'.tr()),
                      _buildSubHeader('max'.tr()),
                      // Third level sub-headers
                      _buildSubHeader('rate'.tr()),
                      _buildSubHeader('max'.tr(), isLast: true),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainSubHeader(String text, {bool isFirst = false, bool isLast = false}) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFD038), // Primary header color
          border: Border(
            right: !isLast ? const BorderSide(color: Color(0xFF161616), width: 1) : BorderSide.none,
            top: const BorderSide(color: Color(0xFF161616), width: 1),
          ),
          borderRadius: isLast ? const BorderRadius.only(topRight: Radius.circular(8)) : BorderRadius.zero,
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: const Color(0xFF101010),
              fontSize: 14.fs,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubHeader(String text, {bool isLast = false}) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF6F5502), // Badge color from Figma
          border: Border(
            right: !isLast ? const BorderSide(color: Color(0xFF161616), width: 1) : BorderSide.none,
          ),
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: const Color(0xFFFFD038), // Primary color text
              fontSize: 14.fs,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildSubColumnRows(CommissionBetListThirdList item) {
    final List<Widget> rows = [];

    // Build rows for each team level (0-3)
    for (int teamLevel = 0; teamLevel <= 3; teamLevel++) {
      rows.add(_buildSubColumnRow(item, teamLevel, teamLevel == 3));
    }

    return rows;
  }

  Widget _buildSubColumnRow(CommissionBetListThirdList item, int teamLevel, bool isLast) {
    return SizedBox(
      height: 30.gw,
      child: Row(
        children: [
          // Team level cell
          Container(
            width: 94.gw,
            decoration: BoxDecoration(
              color: const Color(0xFF212121), // Data row color
              border: const Border(
                right: BorderSide(color: Color(0xFF161616), width: 1),
                bottom: BorderSide(color: Color(0xFF161616), width: 1),
              ),
              borderRadius: isLast ? const BorderRadius.only(bottomLeft: Radius.circular(8)) : BorderRadius.zero,
            ),
            child: Center(
              child: Text(
                'Level $teamLevel',
                style: TextStyle(
                  color: const Color(0xFFB4B3B3),
                  fontSize: 14.fs,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          // Data cells for each child level
          Expanded(
            child: Row(
              children: [
                // First level (childLevel = 1)
                ..._buildDataCells(item, teamLevel, 1),
                // Second level (childLevel = 2)
                ..._buildDataCells(item, teamLevel, 2),
                // Third level (childLevel = 3)
                ..._buildDataCells(item, teamLevel, 3, isLast: isLast),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildDataCells(CommissionBetListThirdList item, int teamLevel, int childLevel, {bool isLast = false}) {
    final record = item.list
        ?.where(
          (element) => element.teamLevel == teamLevel && element.childLevel == childLevel,
        )
        .firstOrNull;

    final rate = '${((record?.commissionRate ?? 0) * 100).toStringAsFixed(2)}%';
    final cap = formatNumberWithChineseUnits((record?.commissionCap ?? 0).toDouble());

    return [
      // Rate cell
      Expanded(
        child: Container(
          decoration: const BoxDecoration(
            color: Color(0xFF212121),
            border: Border(
              right: BorderSide(color: Color(0xFF161616), width: 1),
              bottom: BorderSide(color: Color(0xFF161616), width: 1),
            ),
          ),
          child: Center(
            child: Text(
              rate,
              style: TextStyle(
                color: const Color(0xFFB4B3B3),
                fontSize: 14.fs,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ),
      // Cap cell
      Expanded(
        child: Container(
          decoration: BoxDecoration(
            color: const Color(0xFF212121),
            border: Border(
              right:
                  (childLevel == 3 && isLast) ? BorderSide.none : const BorderSide(color: Color(0xFF161616), width: 1),
              bottom: const BorderSide(color: Color(0xFF161616), width: 1),
            ),
            borderRadius: (childLevel == 3 && isLast)
                ? const BorderRadius.only(bottomRight: Radius.circular(8))
                : BorderRadius.zero,
          ),
          child: Center(
            child: Text(
              cap,
              style: TextStyle(
                color: const Color(0xFFB4B3B3),
                fontSize: 14.fs,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ),
    ];
  }
}

String formatNumberWithChineseUnits(double number) {
  if (number >= 100000000) {
    double value = number / 100000000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}亿';
  } else if (number >= 1000000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  } else if (number >= 10000) {
    double value = number / 10000;
    return '${value % 1 == 0 ? value.toStringAsFixed(0) : value.toStringAsFixed(0)}万';
  }
  return number.toStringAsFixed(0);
}

class RechargeCommission extends StatelessWidget {
  const RechargeCommission({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: BlocBuilder<CommissionPlanCubit, CommissionPlanState>(
        builder: (context, state) {
          return AnimationLimiter(
              child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => ScaleAnimation(
                scale: 0.5,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                _buildCommissionTable(list: state.commissionRechargeEntity?.list?.reversed.toList()),
                SizedBox(height: 24.gw),
                CommonCard(
                  color: context.theme.cardColor,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTitle('${'recharge_commission_rules'.tr()}:', context),
                      SizedBox(height: 10.gw),
                      _buildRules(context),
                    ],
                  ),
                ),
              ],
            ),
          ));
        },
      ),
    );
  }

  Widget _buildTitle(String text, BuildContext context) {
    return Text(
      text,
      style: context.textTheme.primary.fs16.w500,
    );
  }

  Widget _buildCommissionTable({required List<CommissionRechargeList>? list}) {
    final columns = [
      CommonTableColumn(
        title: 'team_level'.tr(),
        key: 'level',
        flex: 1,
        alignment: TextAlign.center,
      ),
      CommonTableColumn(
        title: 'first_level_subordinate'.tr(),
        key: 'commission',
        flex: 1,
        alignment: TextAlign.center,
      ),
    ];

    final tableData = list
            ?.map((item) => [
                  '${item.teamLevel}级',
                  '${((item.commissionRate ?? 0) * 100).toStringAsFixed(1).replaceAll('.0', '')}%',
                ])
            .toList() ??
        [];

    return CommonTable(
      columns: columns,
      data: tableData,
      headerHeight: 30,
      rowHeight: 30,
    );
  }

  Widget _buildRules(BuildContext context) {
    final rules = [
      'commission_rule_1'.tr(),
      'commission_rule_2'.tr(),
      'commission_rule_3'.tr(),
      'commission_rule_4'.tr(),
    ];

    return Padding(
      padding: EdgeInsets.all(10.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: rules
            .map((rule) => Padding(
                  padding: EdgeInsets.only(bottom: 8.gw),
                  child: Text(
                    rule,
                    style: context.textTheme.title,
                  ),
                ))
            .toList(),
      ),
    );
  }
}
